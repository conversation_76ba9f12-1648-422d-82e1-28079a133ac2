<?php
/**
 * Bot Username Retrieval Script
 * 
 * This script retrieves the usernames of all bots using their tokens
 * by calling the Telegram Bot API's getMe method.
 */

// Bot tokens from bot.php
$botTokens = [
    '1'  => '7324085708:AAECLspc6xqyZZ7lekSsm1ZS6PDhsWRejAE',
    '2'  => '7734048562:AAFPk0NBRHwGyFb-hDtt_Br9i6HlZ5IKd1g',
    '3'  => '8000823167:AAG139icfPTOrXUDbR1lEtYOBsJ2IqzcU7E',
    '4'  => '8121324542:AAFsEWUTlbX4_BcaTE8XAoA2WYO2sxHhwKQ',
    '5'  => '7043095943:AAGhPspmxSlvT1yzT9ARIcT3FFr9pO5FlxI',
    '6'  => '8070640534:AAF0SzNYuqxOQ8DvoIzpgA9LnNjAGWE9gho',
    '7'  => '7414728357:AAFWdAq0faNm2Yczhm0qNAX0-Tqo1u6jrAQ',
    '8'  => '7595655197:AAE-LVH3ao4rRRCeOMHdnqw_hgv7tMxPbiI',
    '9'  => '7631470660:AAGF6R8_udL0tWVqZWTFF43Z8wdYQoRDYSU',
    '10' => '8181651796:AAE2aVmc52tPVBofhzj5RbVvPNZKWQiYL3o',
    '11' => '7807349163:AAGr1S0OS4O09YP-PKxVTXQwh5fgss3VdS4',
    '12' => '7224250149:AAFg8tIbi8xsJHVLZSVv52NHtAfxyJ7p7IA',
    '13' => '7324085708:AAECLspc6xqyZZ7lekSsm1ZS6PDhsWRejAE',
    '14' => '7734048562:AAFPk0NBRHwGyFb-hDtt_Br9i6HlZ5IKd1g',
    '15' => '8000823167:AAG139icfPTOrXUDbR1lEtYOBsJ2IqzcU7E',
    '16' => '8121324542:AAFsEWUTlbX4_BcaTE8XAoA2WYO2sxHhwKQ',
    '17' => '7043095943:AAGhPspmxSlvT1yzT9ARIcT3FFr9pO5FlxI',
    '18' => '8070640534:AAF0SzNYuqxOQ8DvoIzpgA9LnNjAGWE9gho',
    '19' => '7414728357:AAFWdAq0faNm2Yczhm0qNAX0-Tqo1u6jrAQ',
    '20' => '7595655197:AAE-LVH3ao4rRRCeOMHdnqw_hgv7tMxPbiI',
    '21' => '7631470660:AAGF6R8_udL0tWVqZWTFF43Z8wdYQoRDYSU',
    '22' => '8181651796:AAE2aVmc52tPVBofhzj5RbVvPNZKWQiYL3o',
    '23' => '7807349163:AAGr1S0OS4O09YP-PKxVTXQwh5fgss3VdS4',
    '24' => '7224250149:AAFg8tIbi8xsJHVLZSVv52NHtAfxyJ7p7IA',
    '25' => '7324085708:AAECLspc6xqyZZ7lekSsm1ZS6PDhsWRejAE',
    '26' => '7734048562:AAFPk0NBRHwGyFb-hDtt_Br9i6HlZ5IKd1g',
    '27' => '8000823167:AAG139icfPTOrXUDbR1lEtYOBsJ2IqzcU7E',
    '28' => '8121324542:AAFsEWUTlbX4_BcaTE8XAoA2WYO2sxHhwKQ',
    '29' => '7043095943:AAGhPspmxSlvT1yzT9ARIcT3FFr9pO5FlxI',
    '30' => '8070640534:AAF0SzNYuqxOQ8DvoIzpgA9LnNjAGWE9gho',
    '31' => '7414728357:AAFWdAq0faNm2Yczhm0qNAX0-Tqo1u6jrAQ',
    '32' => '7595655197:AAE-LVH3ao4rRRCeOMHdnqw_hgv7tMxPbiI',
    '33' => '7631470660:AAGF6R8_udL0tWVqZWTFF43Z8wdYQoRDYSU',
    '34' => '8181651796:AAE2aVmc52tPVBofhzj5RbVvPNZKWQiYL3o',
    '35' => '7807349163:AAGr1S0OS4O09YP-PKxVTXQwh5fgss3VdS4',
    '36' => '7224250149:AAFg8tIbi8xsJHVLZSVv52NHtAfxyJ7p7IA',
    '37' => '8171836495:AAFUHd4K7OcuCfnXVqpEoQFBLd4bhxf4maA',
    '38' => '7309785917:AAEDGLAsBw5yEraoPhSO_PfYCkvSyNwGHts',
    '39' => '7464545372:AAGiey-QGPxpfVnBMB3eaMe-MkBV5JVAJus',
    '40' => '7504410338:AAF7rlo9UZAAwiGl6WMwQ8DqQAfQGCTii-o',
    '41' => '7464545372:AAGiey-QGPxpfVnBMB3eaMe-MkBV5JVAJus',
    '42' => '7504410338:AAF7rlo9UZAAwiGl6WMwQ8DqQAfQGCTii-o',
    '43' => '7611709153:AAGEjbgr-KZcyDMn7Z5dfQwZZPgLSHssp6Y',
    '44' => '8000444345:AAHicXHhUnRptpI3yp26MB0Af_9rWC9t3QE',
];

function getBotInfo($botToken) {
    $apiURL = "https://api.telegram.org/bot{$botToken}/getMe";
    
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'ignore_errors' => true,
            'timeout' => 10
        ]
    ]);
    
    $response = file_get_contents($apiURL, false, $context);
    
    if ($response === false) {
        return ['error' => 'No response from API'];
    }
    
    $data = json_decode($response, true);
    
    if (!$data || !isset($data['ok']) || $data['ok'] !== true) {
        $error = isset($data['description']) ? $data['description'] : 'Unknown error';
        return ['error' => $error];
    }
    
    return [
        'success' => true,
        'id' => $data['result']['id'],
        'username' => $data['result']['username'],
        'first_name' => $data['result']['first_name'],
        'is_bot' => $data['result']['is_bot']
    ];
}

echo "Bot Username Retrieval Report\n";
echo "=============================\n\n";

$uniqueTokens = [];
$botUsernames = [];
$errors = [];

// First, identify unique tokens to avoid duplicate API calls
foreach ($botTokens as $botId => $token) {
    if (!isset($uniqueTokens[$token])) {
        $uniqueTokens[$token] = [];
    }
    $uniqueTokens[$token][] = $botId;
}

echo "Processing " . count($uniqueTokens) . " unique tokens for " . count($botTokens) . " bot slots...\n\n";

// Get info for each unique token
foreach ($uniqueTokens as $token => $botIds) {
    $info = getBotInfo($token);
    
    if (isset($info['success'])) {
        foreach ($botIds as $botId) {
            $botUsernames[$botId] = [
                'username' => $info['username'],
                'first_name' => $info['first_name'],
                'id' => $info['id']
            ];
        }
        echo "✓ Token ending in ..." . substr($token, -10) . " → @{$info['username']} ({$info['first_name']}) - Used by bots: " . implode(', ', $botIds) . "\n";
    } else {
        foreach ($botIds as $botId) {
            $errors[$botId] = $info['error'];
        }
        echo "✗ Token ending in ..." . substr($token, -10) . " → ERROR: {$info['error']} - Affects bots: " . implode(', ', $botIds) . "\n";
    }
    
    // Small delay to avoid rate limiting
    usleep(100000); // 0.1 second
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "COMPLETE BOT USERNAME LIST\n";
echo str_repeat("=", 50) . "\n";

for ($i = 1; $i <= 44; $i++) {
    if (isset($botUsernames[$i])) {
        $bot = $botUsernames[$i];
        echo sprintf("Bot #%-2d: @%-15s (%s)\n", $i, $bot['username'], $bot['first_name']);
    } else {
        $error = isset($errors[$i]) ? $errors[$i] : 'Unknown error';
        echo sprintf("Bot #%-2d: ERROR - %s\n", $i, $error);
    }
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "SUMMARY\n";
echo str_repeat("=", 50) . "\n";

$validBots = count($botUsernames);
$errorBots = count($errors);

echo "Valid bots: {$validBots}\n";
echo "Error bots: {$errorBots}\n";
echo "Total unique usernames: " . count(array_unique(array_column($botUsernames, 'username'))) . "\n";

if ($errorBots > 0) {
    echo "\nBots with errors:\n";
    foreach ($errors as $botId => $error) {
        echo "  Bot #{$botId}: {$error}\n";
    }
}

echo "\nUnique usernames in use:\n";
$uniqueUsernames = array_unique(array_column($botUsernames, 'username'));
sort($uniqueUsernames);
foreach ($uniqueUsernames as $username) {
    $count = count(array_filter($botUsernames, function($bot) use ($username) {
        return $bot['username'] === $username;
    }));
    echo "  @{$username} (used by {$count} bot" . ($count > 1 ? 's' : '') . ")\n";
}

?>
