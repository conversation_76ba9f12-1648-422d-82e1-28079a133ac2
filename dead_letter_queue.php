<?php
/**
 * Dead Letter Queue Management
 * 
 * This file manages reactions that have failed multiple times and need manual intervention.
 */

define('DEAD_LETTER_FILE', __DIR__ . '/dead_letter_queue.json');
define('LOGFILE', __DIR__ . '/bot_reactions.log');

function logActivity($message) {
    file_put_contents(LOGFILE, date('[Y-m-d H:i:s] ') . $message . "\n", FILE_APPEND);
}

function loadDeadLetterQueue() {
    if (!file_exists(DEAD_LETTER_FILE)) {
        return [];
    }
    
    $content = file_get_contents(DEAD_LETTER_FILE);
    return $content ? json_decode($content, true) : [];
}

function saveDeadLetterQueue($queue) {
    file_put_contents(DEAD_LETTER_FILE, json_encode($queue, JSON_PRETTY_PRINT));
}

function addToDeadLetterQueue($reaction, $reason) {
    $deadQueue = loadDeadLetterQueue();
    
    $deadReaction = [
        'original_reaction' => $reaction,
        'reason' => $reason,
        'failed_at' => time(),
        'failed_date' => date('Y-m-d H:i:s'),
        'retry_count' => $reaction['retry_count'] ?? 0,
        'id' => uniqid('dead_', true)
    ];
    
    $deadQueue[] = $deadReaction;
    saveDeadLetterQueue($deadQueue);
    
    logActivity("DEAD_LETTER: Added reaction to dead letter queue - Bot #{$reaction['bot_id']} → '{$reaction['emoji']}' (Channel: {$reaction['chat_id']}, Message: {$reaction['message_id']}) - Reason: {$reason}");
}

function getDeadLetterStats() {
    $deadQueue = loadDeadLetterQueue();
    
    $stats = [
        'total' => count($deadQueue),
        'by_bot' => [],
        'by_channel' => [],
        'by_reason' => []
    ];
    
    foreach ($deadQueue as $item) {
        $reaction = $item['original_reaction'];
        $botId = $reaction['bot_id'];
        $chatId = $reaction['chat_id'];
        $reason = $item['reason'];
        
        // Count by bot
        if (!isset($stats['by_bot'][$botId])) {
            $stats['by_bot'][$botId] = 0;
        }
        $stats['by_bot'][$botId]++;
        
        // Count by channel
        if (!isset($stats['by_channel'][$chatId])) {
            $stats['by_channel'][$chatId] = 0;
        }
        $stats['by_channel'][$chatId]++;
        
        // Count by reason
        if (!isset($stats['by_reason'][$reason])) {
            $stats['by_reason'][$reason] = 0;
        }
        $stats['by_reason'][$reason]++;
    }
    
    return $stats;
}

function clearDeadLetterQueue() {
    if (file_exists(DEAD_LETTER_FILE)) {
        unlink(DEAD_LETTER_FILE);
        logActivity("DEAD_LETTER: Cleared dead letter queue");
        return true;
    }
    return false;
}

function retryDeadLetterItem($itemId) {
    $deadQueue = loadDeadLetterQueue();
    $found = false;
    
    foreach ($deadQueue as $index => $item) {
        if ($item['id'] === $itemId) {
            $reaction = $item['original_reaction'];
            
            // Reset retry count and reschedule
            $reaction['retry_count'] = 0;
            $reaction['scheduled_time'] = time() + 60; // Retry in 1 minute
            
            // Add back to main queue
            require_once 'bot.php';
            $mainQueue = loadQueue();
            $mainQueue[] = $reaction;
            saveQueue($mainQueue);
            
            // Remove from dead letter queue
            unset($deadQueue[$index]);
            $deadQueue = array_values($deadQueue); // Re-index array
            saveDeadLetterQueue($deadQueue);
            
            logActivity("DEAD_LETTER: Retrying item {$itemId} - Bot #{$reaction['bot_id']} → '{$reaction['emoji']}' (Channel: {$reaction['chat_id']}, Message: {$reaction['message_id']})");
            $found = true;
            break;
        }
    }
    
    return $found;
}

// If accessed directly via web, show dead letter queue status
if (isset($_SERVER['HTTP_HOST'])) {
    header('Content-Type: text/plain');
    
    echo "Dead Letter Queue Status\n";
    echo "========================\n\n";
    
    $stats = getDeadLetterStats();
    
    echo "Total failed reactions: {$stats['total']}\n\n";
    
    if ($stats['total'] > 0) {
        echo "Failures by Bot:\n";
        foreach ($stats['by_bot'] as $botId => $count) {
            echo "  Bot #{$botId}: {$count} failures\n";
        }
        
        echo "\nFailures by Channel:\n";
        foreach ($stats['by_channel'] as $chatId => $count) {
            echo "  Channel {$chatId}: {$count} failures\n";
        }
        
        echo "\nFailures by Reason:\n";
        foreach ($stats['by_reason'] as $reason => $count) {
            echo "  {$reason}: {$count} failures\n";
        }
        
        echo "\nRecent Failed Reactions:\n";
        $deadQueue = loadDeadLetterQueue();
        $recent = array_slice(array_reverse($deadQueue), 0, 10);
        
        foreach ($recent as $item) {
            $reaction = $item['original_reaction'];
            echo "  {$item['failed_date']} - Bot #{$reaction['bot_id']} → '{$reaction['emoji']}' (Channel: {$reaction['chat_id']}, Message: {$reaction['message_id']}) - {$item['reason']}\n";
        }
        
        echo "\nActions:\n";
        echo "  - Review and fix underlying issues\n";
        echo "  - Use retryDeadLetterItem(\$itemId) to retry specific items\n";
        echo "  - Use clearDeadLetterQueue() to clear all failed items\n";
    } else {
        echo "No failed reactions in dead letter queue.\n";
    }
}

?>
